local isLoadingScreenActive = true
local loadingProgress = 0

local function updateLoadingProgress(progress, text)
    if isLoadingScreenActive then
        SendLoadingScreenMessage(json.encode({
            eventName = 'loadProgress',
            loadFraction = progress / 100,
            loadingText = text or "Načítání..."
        }))
    end
end

RegisterNUICallback('loadingComplete', function(data, cb)
    if isLoadingScreenActive then
        print("^2[valic_loading] Loading screen fade out completed, shutting down^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
    cb('ok')
end)

Citizen.CreateThread(function()
    local startTime = GetGameTimer()
    local maxLoadTime = 45000

    while isLoadingScreenActive do
        local gameProgress = nil

        local success, result = pcall(function()
            return GetLoadingScreenLoadFraction()
        end)

        if success and result then
            gameProgress = result
        end

        local progressPercent = 0

        if gameProgress and gameProgress > 0 then
            progressPercent = math.floor(gameProgress * 100)
        else
            local elapsedTime = GetGameTimer() - startTime
            progressPercent = math.min(math.floor((elapsedTime / maxLoadTime) * 100), 99)
        end

        if progressPercent ~= loadingProgress then
            loadingProgress = progressPercent

            local loadingText = "Načítání..."
            if progressPercent >= 10 then loadingText = "Načítání světa..." end
            if progressPercent >= 25 then loadingText = "Načítání vozidel..." end
            if progressPercent >= 50 then loadingText = "Načítání objektů..." end
            if progressPercent >= 75 then loadingText = "Dokončování..." end
            if progressPercent >= 90 then loadingText = "Téměř hotovo..." end

            updateLoadingProgress(progressPercent, loadingText)
        end

        local elapsedTime = GetGameTimer() - startTime
        if progressPercent >= 100 or elapsedTime >= maxLoadTime then
            Citizen.Wait(2000)
            ShutdownLoadingScreen()
            isLoadingScreenActive = false
            break
        end

        Citizen.Wait(100)
    end
end)

RegisterNetEvent('valic_loading:updateProgress')
AddEventHandler('valic_loading:updateProgress', function(progress, text)
    updateLoadingProgress(progress, text)
end)

RegisterNetEvent('valic_loading:closeLoadingScreen')
AddEventHandler('valic_loading:closeLoadingScreen', function()
    if isLoadingScreenActive then
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

AddEventHandler('playerSpawned', function()
    if isLoadingScreenActive then
        Citizen.Wait(1000)
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

Citizen.CreateThread(function()
    Citizen.Wait(60000)
    if isLoadingScreenActive then
        print("^3[valic_loading] Loading screen timeout - force closing^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)