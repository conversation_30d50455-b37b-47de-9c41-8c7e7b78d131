RegisterNetEvent('valic_loading:serverUpdateProgress')
AddEventHandler('valic_loading:serverUpdateProgress', function(progress, text)
    local source = source
    TriggerClientEvent('valic_loading:updateProgress', source, progress, text)
end)

RegisterNetEvent('valic_loading:serverCloseLoadingScreen')
AddEventHandler('valic_loading:serverCloseLoadingScreen', function()
    local source = source
    TriggerClientEvent('valic_loading:closeLoadingScreen', source)
end)

function BroadcastLoadingProgress(progress, text)
    TriggerClientEvent('valic_loading:updateProgress', -1, progress, text)
end

function BroadcastCloseLoadingScreen()
    TriggerClientEvent('valic_loading:closeLoadingScreen', -1)
end

exports('updateLoadingProgress', function(playerId, progress, text)
    TriggerClientEvent('valic_loading:updateProgress', playerId, progress, text)
end)

exports('closeLoadingScreen', function(playerId)
    TriggerClientEvent('valic_loading:closeLoadingScreen', playerId)
end)

exports('broadcastLoadingProgress', BroadcastLoadingProgress)
exports('broadcastCloseLoadingScreen', BroadcastCloseLoadingScreen)

print("^2[valic_loading] Server script loaded successfully^7")

AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print("^3[valic_loading] Player " .. name .. " is connecting (ID: " .. source .. ")^7")
end)

AddEventHandler('playerDropped', function(reason)
    local source = source
    print("^1[valic_loading] Player disconnected (ID: " .. source .. ") - Reason: " .. reason .. "^7")
end)