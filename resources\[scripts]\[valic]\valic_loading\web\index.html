<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>valic_loading</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link rel="icon" type="image/png" href="assets/logos/logo.png">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Audiowide&family=Freckle+Face&display=swap');
        
        @keyframes zoom {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        body {
            font-family: 'Poppins', sans-serif;
            height: 100vh;
            overflow: hidden;
            color: white;
            position: relative;
        }

        .background-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.082));
            z-index: 1;
        }

        .background-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            animation: zoom 20s infinite alternate;
            z-index: 0;
        }
        
        .progress-container {
            height: 12px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #093c74, #443868);
            border-radius: 6px;
            transition: width 0.3s ease;
        }
        
        .music-player {
            background: rgba(0, 0, 0, 0.335);
            backdrop-filter: blur(10px);
            border-radius: 15px;
        }
        
        .music-timeline {
            -webkit-appearance: none;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            cursor: pointer;
            transition: background .3s ease;
        }
        
        .music-timeline::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
        }
        
        .social-icon {
            transition: all 0.3s ease;
        }
        
        .social-icon:hover {
            transform: translateY(-5px);
        }
        
        .discord-icon:hover {
            color: #5865F2;
        }
        
        .youtube-icon:hover {
            color: #FF0000;
        }
        
        .tiktok-icon:hover {
            color: #25F4EE;
        }
        
        .store-icon:hover {
            color: #FF6B00;
        }

        .content {
            position: relative;
            z-index: 2;
        }

        .main-title {
            font-family: "Audiowide", sans-serif;
        }
    </style>
</head>
<body class="relative flex items-center justify-center">
    <div class="background-image" id="backgroundImage"></div>
    <div class="background-overlay"></div>

    <div class="content w-full h-full relative">
        <div class="absolute top-6 left-6">
            <img src="assets/logos/logo.png" alt="DiverseRP Logo" class="h-16">
        </div>

        <div class="flex items-start justify-center pt-16">
            <div class="text-center max-w-2xl px-6">
                <h1 class="text-4xl md:text-5xl font-bold mb-4 main-title">Vítejte na Diverse RP</h1>
                <p class="text-lg md:text-xl mb-8 opacity-80" id="loadingText">Inicializace...</p>
            </div>
        </div>

        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2 w-full max-w-2xl px-6">
            <div class="mb-2 flex justify-between items-center">
                <div class="progress-container flex-grow mr-4">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <span id="progressPercentage" class="font-semibold text-sm">0%</span>
            </div>
        </div>
        
        <div class="absolute bottom-6 left-6 w-80 music-player p-4">
            <div class="flex items-center space-x-4 mb-3">
                <img id="albumArt" src="assets/background-error/empty-album.png" alt="Album Art" class="w-14 h-14 rounded-lg">
                <div class="flex-1">
                    <h3 class="font-semibold truncate" id="songTitle">Načítám songy...</h3>
                    <p class="text-xs opacity-70 truncate" id="songArtist">Načítám jméno tvůrce songy...</p>
                </div>
            </div>
            
            <input type="range" min="0" max="100" value="0" class="w-full music-timeline mb-2" id="musicTimeline">
            
            <div class="flex items-center justify-between">
                <div class="flex space-x-4">
                    <button class="text-white hover:text-blue-400 transition" id="prevBtn">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button class="text-white hover:text-blue-400 transition text-xl" id="playPauseBtn">
                        <i class="fas fa-play" id="playPauseIcon"></i>
                    </button>
                    <button class="text-white hover:text-blue-400 transition" id="nextBtn">
                        <i class="fas fa-step-forward"></i>
                    </button>
                </div>
                <div class="text-xs opacity-70" id="timeDisplay">0:00 / 0:00</div>
                <div class="flex items-center space-x-2">
                    <button class="text-white hover:text-blue-400 transition" id="volumeBtn">
                        <i class="fas fa-volume-down" id="volumeIcon"></i>
                    </button>
                    <input type="range" min="0" max="1" step="0.01" value="0.1" class="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer" id="volumeSlider">
                </div>
            </div>
        </div>
        
        <div class="absolute top-6 right-6 text-right">
            <a href="https://discord.com/channels/1371147926642102333/1391171469723041942" target="_blank" class="bg-black/30 backdrop-blur-sm rounded-lg p-3 mb-3 block hover:bg-black/40 transition-all duration-300">
                <p class="text-xs text-white/80 mb-1">Chceš svou fotku v loadingu?</p>
                <p class="text-sm text-blue-300 hover:text-blue-200">📸 Pošli ji zde!</p>
            </a>

            <div class="bg-black/30 backdrop-blur-sm rounded-lg p-3 flex items-center space-x-3">
                <img id="authorAvatar" src="assets/profiles/remiix.png" alt="Author Avatar" class="w-8 h-8 rounded-full">
                <div class="text-left">
                    <p class="text-xs text-white/60">Fotka od:</p>
                    <p class="text-sm text-white font-medium" id="authorName">𝙧𝙚𝙢𝙞𝙞𝙭</p>
                </div>
            </div>
        </div>

        <div class="absolute bottom-6 right-6 flex space-x-4">
            <a href="https://discord.gg/Yn3CBzU4yb" target="_blank" class="social-icon discord-icon text-white text-2xl hover:text-3xl">
                <i class="fab fa-discord"></i>
            </a>
            <a href="https://www.youtube.com/@Diverse-RP" target="_blank" class="social-icon youtube-icon text-white text-2xl hover:text-3xl">
                <i class="fab fa-youtube"></i>
            </a>
            <a href="https://www.tiktok.com/@diverse.rp" target="_blank" class="social-icon tiktok-icon text-white text-2xl hover:text-3xl">
                <i class="fab fa-tiktok"></i>
            </a>
        </div>
    </div>
    
    <script src="game.js"></script>
    <script>
        const backgroundElement = document.getElementById('backgroundImage');
        const authorAvatar = document.getElementById('authorAvatar');
        const authorName = document.getElementById('authorName');

        const backgroundImages = [
            {
                image: 'assets/background-imgs/1.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/2.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/3.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/4.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/5.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/6.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/7.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/8.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/9.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/10.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/11.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/12.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/13.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/14.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            },
            {
                image: 'assets/background-imgs/15.png',
                author: '𝙧𝙚𝙢𝙞𝙞𝙭',
                avatar: 'assets/profiles/remiix.png' //64x64 maximalne fotka vole!!!
            }
        ];

        let currentBgIndex = 0;

        function setRandomBackground() {
            const randomIndex = Math.floor(Math.random() * backgroundImages.length);
            const selectedBg = backgroundImages[randomIndex];
            backgroundElement.style.backgroundImage = `url('${selectedBg.image}')`;

            // Aktualizuj informace o autorovi
            authorAvatar.src = selectedBg.avatar;
            authorName.textContent = selectedBg.author;
        }

        function changeBackground() {
            setRandomBackground();
            const randomInterval = Math.random() * (8000 - 5000) + 5000;
            setTimeout(changeBackground, randomInterval);
        }

        changeBackground();

        const audio = new Audio();
        const playPauseBtn = document.getElementById('playPauseBtn');
        const playPauseIcon = document.getElementById('playPauseIcon');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const volumeBtn = document.getElementById('volumeBtn');
        const volumeIcon = document.getElementById('volumeIcon');
        const volumeSlider = document.getElementById('volumeSlider');
        const musicTimeline = document.getElementById('musicTimeline');
        const timeDisplay = document.getElementById('timeDisplay');
        const songTitle = document.getElementById('songTitle');
        const songArtist = document.getElementById('songArtist');
        const albumArt = document.getElementById('albumArt');
        
        const playlist = [
            {
                title: "Waves",
                artist: "Prod. Riddiman",
                src: "music/song1.mp3",
                cover: "assets/songs-img/song1.png"
            },
            {
                title: "HEAVY RAIN",
                artist: "Malu",
                src: "music/song2.mp3",
                cover: "assets/songs-img/song2.png"
            },
        ];
        
        let currentTrack = Math.floor(Math.random() * playlist.length);
        let isPlaying = true;

        function setVolume(volume) {
            audio.volume = volume;
            volumeSlider.value = volume;

            if (volume > 0.5) {
                volumeIcon.className = "fas fa-volume-up";
            } else if (volume > 0) {
                volumeIcon.className = "fas fa-volume-down";
            } else {
                volumeIcon.className = "fas fa-volume-mute";
            }
        }
        
        function loadTrack(trackIndex) {
            const track = playlist[trackIndex];
            audio.src = track.src;
            songTitle.textContent = track.title;
            songArtist.textContent = track.artist;
            albumArt.src = track.cover;
            
            audio.addEventListener('loadedmetadata', () => {
                updateTimeDisplay();
            });
            
            if (isPlaying) {
                audio.play().catch(e => console.log("Autoplay was prevented."));
            }
        }
        
        playPauseBtn.addEventListener('click', () => {
            if (isPlaying) {
                audio.pause();
                playPauseIcon.classList.remove('fa-pause');
                playPauseIcon.classList.add('fa-play');
            } else {
                audio.play();
                playPauseIcon.classList.remove('fa-play');
                playPauseIcon.classList.add('fa-pause');
            }
            isPlaying = !isPlaying;
        });
        
        prevBtn.addEventListener('click', () => {
            currentTrack = (currentTrack - 1 + playlist.length) % playlist.length;
            loadTrack(currentTrack);
        });
        
        nextBtn.addEventListener('click', () => {
            currentTrack = (currentTrack + 1) % playlist.length;
            loadTrack(currentTrack);
        });
        
        volumeSlider.addEventListener('input', (e) => {
            setVolume(e.target.value);
        });

        volumeBtn.addEventListener('click', () => {
            if (audio.volume > 0) {
                setVolume(0);
            } else {
                setVolume(0.1);
            }
        });
        
        musicTimeline.addEventListener('input', () => {
            const seekTime = (audio.duration / 100) * musicTimeline.value;
            audio.currentTime = seekTime;
        });
        
        audio.addEventListener('timeupdate', () => {
            const currentTime = audio.currentTime;
            const duration = audio.duration;
            if (duration > 0) {
                const progressPercent = (currentTime / duration) * 100;
                musicTimeline.value = progressPercent;
                musicTimeline.style.background = `linear-gradient(to right, #443868 ${progressPercent}%, rgba(255, 255, 255, 0.3) ${progressPercent}%)`;
            }
            updateTimeDisplay();
        });
        
        audio.addEventListener('ended', () => {
            nextBtn.click();
        });
        
        function updateTimeDisplay() {
            const currentMins = Math.floor(audio.currentTime / 60);
            const currentSecs = Math.floor(audio.currentTime % 60).toString().padStart(2, '0');
            const duration = isNaN(audio.duration) ? 0 : audio.duration;
            const durationMins = Math.floor(duration / 60);
            const durationSecs = Math.floor(duration % 60).toString().padStart(2, '0');
            
            timeDisplay.textContent = `${currentMins}:${currentSecs} / ${durationMins}:${durationSecs}`;
        }
        
        playPauseIcon.classList.remove('fa-play');
        playPauseIcon.classList.add('fa-pause');
        setVolume(0.1);
        loadTrack(currentTrack);
    </script>
</body>
</html>